const { Client } = require('pg');
require('dotenv').config();

// Configuración de la base de datos desde .env
const dbConfig = {
  host: process.env.DATABASE_HOST || 'aws-0-us-east-2.pooler.supabase.com',
  port: process.env.DATABASE_PORT || 5432,
  database: process.env.DATABASE_NAME || 'postgres',
  user: process.env.DATABASE_USERNAME || 'postgres.ktykxlboodjvnlabbwoy',
  password: process.env.DATABASE_PASSWORD || 'GranCH@1844',
  ssl: { rejectUnauthorized: false }
};

async function checkLocaleIssues() {
  const client = new Client(dbConfig);
  
  try {
    await client.connect();
    console.log('🔗 Conectado a la base de datos');
    
    // Verificar registros con locale null en cada tabla
    const tables = ['fields', 'zones', 'districts', 'churches', 'participants'];
    
    console.log('\n🔍 Verificando registros con locale null...\n');
    
    for (const table of tables) {
      try {
        // Verificar si la tabla existe y tiene columna locale
        const tableExists = await client.query(`
          SELECT column_name 
          FROM information_schema.columns 
          WHERE table_name = $1 AND column_name = 'locale'
        `, [table]);
        
        if (tableExists.rows.length === 0) {
          console.log(`⚠️  Tabla '${table}' no tiene columna 'locale'`);
          continue;
        }
        
        // Contar registros con locale null
        const nullLocaleCount = await client.query(`
          SELECT COUNT(*) as count FROM ${table} WHERE locale IS NULL
        `);
        
        // Contar registros con locale no null
        const nonNullLocaleCount = await client.query(`
          SELECT COUNT(*) as count FROM ${table} WHERE locale IS NOT NULL
        `);
        
        // Contar total de registros
        const totalCount = await client.query(`
          SELECT COUNT(*) as count FROM ${table}
        `);
        
        // Obtener ejemplos de registros con locale null (máximo 5)
        let nullExamples, nonNullExamples;

        if (table === 'participants') {
          // Para participants, usar first_name y last_name
          nullExamples = await client.query(`
            SELECT document_id, first_name, last_name, email, locale
            FROM ${table}
            WHERE locale IS NULL
            LIMIT 5
          `);

          nonNullExamples = await client.query(`
            SELECT document_id, first_name, last_name, email, locale
            FROM ${table}
            WHERE locale IS NOT NULL
            LIMIT 3
          `);
        } else {
          // Para otras tablas, usar name
          nullExamples = await client.query(`
            SELECT document_id, name, locale
            FROM ${table}
            WHERE locale IS NULL
            LIMIT 5
          `);

          nonNullExamples = await client.query(`
            SELECT document_id, name, locale
            FROM ${table}
            WHERE locale IS NOT NULL
            LIMIT 3
          `);
        }
        
        console.log(`📊 Tabla: ${table.toUpperCase()}`);
        console.log(`   Total registros: ${totalCount.rows[0].count}`);
        console.log(`   Con locale null: ${nullLocaleCount.rows[0].count}`);
        console.log(`   Con locale válido: ${nonNullLocaleCount.rows[0].count}`);
        
        if (nullExamples.rows.length > 0) {
          console.log(`   ❌ Ejemplos con locale null:`);
          nullExamples.rows.forEach(row => {
            if (table === 'participants') {
              console.log(`      - ID: ${row.document_id}, Name: ${row.first_name} ${row.last_name}, Email: ${row.email}, Locale: ${row.locale}`);
            } else {
              console.log(`      - ID: ${row.document_id}, Name: ${row.name}, Locale: ${row.locale}`);
            }
          });
        }

        if (nonNullExamples.rows.length > 0) {
          console.log(`   ✅ Ejemplos con locale válido:`);
          nonNullExamples.rows.forEach(row => {
            if (table === 'participants') {
              console.log(`      - ID: ${row.document_id}, Name: ${row.first_name} ${row.last_name}, Email: ${row.email}, Locale: ${row.locale}`);
            } else {
              console.log(`      - ID: ${row.document_id}, Name: ${row.name}, Locale: ${row.locale}`);
            }
          });
        }
        
        console.log('');
        
      } catch (error) {
        console.error(`❌ Error verificando tabla '${table}':`, error.message);
      }
    }
    
    // Verificar específicamente el participante del error
    console.log('🔍 Verificando el participante específico del error...');
    const participantId = 'bdfa292dae8445659186054b27ce20e8';
    
    try {
      const participantCheck = await client.query(`
        SELECT document_id, first_name, last_name, email, locale 
        FROM participants 
        WHERE document_id = $1
      `, [participantId]);
      
      if (participantCheck.rows.length > 0) {
        const participant = participantCheck.rows[0];
        console.log(`✅ Participante encontrado:`);
        console.log(`   - ID: ${participant.document_id}`);
        console.log(`   - Nombre: ${participant.first_name} ${participant.last_name}`);
        console.log(`   - Email: ${participant.email}`);
        console.log(`   - Locale: ${participant.locale}`);
      } else {
        console.log(`❌ Participante con ID ${participantId} no encontrado`);
      }
    } catch (error) {
      console.error(`❌ Error verificando participante específico:`, error.message);
    }
    
    console.log('\n📋 Resumen del análisis completado');
    
  } catch (error) {
    console.error('❌ Error durante la verificación:', error);
  } finally {
    await client.end();
    console.log('🔌 Conexión cerrada');
  }
}

checkLocaleIssues();
