const { Client } = require('pg');

async function diagnoseData() {
  const client = new Client({
    host: 'aws-0-us-east-2.pooler.supabase.com',
    port: 5432,
    database: 'postgres',
    user: 'postgres.ktykxlboodjvnlabbwoy',
    password: 'GranCH@1844',
    ssl: { rejectUnauthorized: false }
  });

  try {
    await client.connect();
    console.log('🔗 Conectado a la base de datos');

    // 1. Verificar si los datos están realmente en la base de datos
    console.log('\n📊 VERIFICACIÓN DE DATOS EN BASE DE DATOS:');
    
    const tables = ['fields', 'zones', 'districts', 'churches'];
    
    for (const table of tables) {
      try {
        const count = await client.query(`SELECT COUNT(*) FROM ${table}`);
        const sample = await client.query(`SELECT * FROM ${table} LIMIT 3`);
        
        console.log(`\n${table.toUpperCase()}:`);
        console.log(`   Total registros: ${count.rows[0].count}`);
        
        if (sample.rows.length > 0) {
          console.log('   Muestra de datos:');
          sample.rows.forEach((row, index) => {
            console.log(`   ${index + 1}. ID: ${row.document_id || row.id}, Nombre: ${row.name}, Locale: ${row.locale}`);
          });
        } else {
          console.log('   ❌ No hay datos');
        }
      } catch (error) {
        console.log(`   ❌ Error accediendo a tabla ${table}: ${error.message}`);
      }
    }

    // 2. Verificar estructura de tablas
    console.log('\n🏗️ VERIFICACIÓN DE ESTRUCTURA DE TABLAS:');
    
    for (const table of tables) {
      try {
        const columns = await client.query(`
          SELECT column_name, data_type, is_nullable 
          FROM information_schema.columns 
          WHERE table_name = $1 
          ORDER BY ordinal_position
        `, [table]);
        
        console.log(`\n${table.toUpperCase()} - Columnas:`);
        columns.rows.forEach(col => {
          console.log(`   - ${col.column_name}: ${col.data_type} (${col.is_nullable === 'YES' ? 'nullable' : 'not null'})`);
        });
      } catch (error) {
        console.log(`   ❌ Error obteniendo estructura de ${table}: ${error.message}`);
      }
    }

    // 3. Verificar configuración de Strapi
    console.log('\n⚙️ CONFIGURACIÓN DE STRAPI:');
    
    const strapiConfig = await client.query(`
      SELECT key, value 
      FROM strapi_core_store_settings 
      WHERE key IN ('plugin_i18n_default_locale', 'strapi_content_types_schema')
      ORDER BY key
    `);
    
    strapiConfig.rows.forEach(config => {
      console.log(`   - ${config.key}:`);
      if (config.key === 'strapi_content_types_schema') {
        console.log('     [Schema muy largo - verificar manualmente]');
      } else {
        console.log(`     ${config.value}`);
      }
    });

    // 4. Verificar si hay datos con published_at NULL
    console.log('\n📝 VERIFICACIÓN DE ESTADO DE PUBLICACIÓN:');
    
    for (const table of tables) {
      try {
        const published = await client.query(`
          SELECT 
            COUNT(*) as total,
            COUNT(published_at) as published,
            COUNT(*) - COUNT(published_at) as unpublished
          FROM ${table}
        `);
        
        const row = published.rows[0];
        console.log(`\n${table.toUpperCase()}:`);
        console.log(`   Total: ${row.total}`);
        console.log(`   Publicados: ${row.published}`);
        console.log(`   No publicados: ${row.unpublished}`);
        
        if (parseInt(row.unpublished) > 0) {
          console.log('   ⚠️ Hay registros sin publicar - esto puede causar que no aparezcan en Strapi');
        }
      } catch (error) {
        console.log(`   ❌ Error verificando publicación en ${table}: ${error.message}`);
      }
    }

    // 5. Verificar el campo específico del error original
    console.log('\n🎯 VERIFICACIÓN DEL CAMPO ESPECÍFICO DEL ERROR:');
    const specificId = 'bdfa292dae8445659186054b27ce20e8';
    
    const specificField = await client.query(`
      SELECT * FROM fields WHERE document_id = $1
    `, [specificId]);
    
    if (specificField.rows.length > 0) {
      console.log('✅ Campo encontrado:');
      const field = specificField.rows[0];
      console.log(`   - Nombre: ${field.name}`);
      console.log(`   - Document ID: ${field.document_id}`);
      console.log(`   - Locale: ${field.locale}`);
      console.log(`   - Publicado: ${field.published_at ? 'Sí' : 'No'}`);
      console.log(`   - Creado: ${field.created_at}`);
    } else {
      console.log('❌ Campo específico no encontrado');
    }

    // 6. Verificar relaciones entre tablas
    console.log('\n🔗 VERIFICACIÓN DE RELACIONES:');
    
    try {
      const relations = await client.query(`
        SELECT 
          f.name as field_name,
          COUNT(z.id) as zones_count,
          COUNT(d.id) as districts_count,
          COUNT(c.id) as churches_count
        FROM fields f
        LEFT JOIN zones z ON z.field_id = f.id
        LEFT JOIN districts d ON d.zone_id = z.id  
        LEFT JOIN churches c ON c.district_id = d.id
        GROUP BY f.id, f.name
        ORDER BY f.name
      `);
      
      relations.rows.forEach(rel => {
        console.log(`   ${rel.field_name}:`);
        console.log(`     - Zonas: ${rel.zones_count}`);
        console.log(`     - Distritos: ${rel.districts_count}`);
        console.log(`     - Iglesias: ${rel.churches_count}`);
      });
    } catch (error) {
      console.log(`   ❌ Error verificando relaciones: ${error.message}`);
    }

  } catch (error) {
    console.error('❌ Error general:', error.message);
  } finally {
    await client.end();
    console.log('\n🔌 Conexión cerrada');
  }
}

diagnoseData();
