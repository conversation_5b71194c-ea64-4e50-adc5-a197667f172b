const { Client } = require('pg');
require('dotenv').config();

// Configuración de la base de datos desde .env
const dbConfig = {
  host: process.env.DATABASE_HOST || 'aws-0-us-east-2.pooler.supabase.com',
  port: process.env.DATABASE_PORT || 5432,
  database: process.env.DATABASE_NAME || 'postgres',
  user: process.env.DATABASE_USERNAME || 'postgres.ktykxlboodjvnlabbwoy',
  password: process.env.DATABASE_PASSWORD || 'GranCH@1844',
  ssl: { rejectUnauthorized: false }
};

async function fixLocaleIssues() {
  const client = new Client(dbConfig);
  
  try {
    await client.connect();
    console.log('🔗 Conectado a la base de datos');
    
    console.log('\n🔧 Iniciando corrección de problemas de localidad...\n');
    
    // 1. Corregir participants con locale null
    console.log('📝 Corrigiendo participants con locale null...');
    
    const participantsToFix = await client.query(`
      SELECT document_id, first_name, last_name, email, locale 
      FROM participants 
      WHERE locale IS NULL
    `);
    
    console.log(`   Encontrados ${participantsToFix.rows.length} participants con locale null`);
    
    if (participantsToFix.rows.length > 0) {
      // Actualizar todos los participants con locale null a 'es'
      const updateResult = await client.query(`
        UPDATE participants 
        SET locale = 'es', updated_at = NOW() 
        WHERE locale IS NULL
      `);
      
      console.log(`   ✅ Actualizados ${updateResult.rowCount} participants a locale 'es'`);
      
      // Mostrar los participants actualizados
      participantsToFix.rows.forEach(participant => {
        console.log(`      - ${participant.first_name} ${participant.last_name} (${participant.email})`);
      });
    }
    
    // 2. Verificar si hay otros registros con locale null (por si acaso)
    console.log('\n🔍 Verificando otras tablas por si hay locale null...');
    
    const tables = ['fields', 'zones', 'districts', 'churches'];
    
    for (const table of tables) {
      const nullCount = await client.query(`
        SELECT COUNT(*) as count FROM ${table} WHERE locale IS NULL
      `);
      
      if (parseInt(nullCount.rows[0].count) > 0) {
        console.log(`   ⚠️ Encontrados ${nullCount.rows[0].count} registros con locale null en ${table}`);
        
        // Corregir automáticamente
        const updateResult = await client.query(`
          UPDATE ${table} 
          SET locale = 'es', updated_at = NOW() 
          WHERE locale IS NULL
        `);
        
        console.log(`   ✅ Corregidos ${updateResult.rowCount} registros en ${table}`);
      } else {
        console.log(`   ✅ Tabla ${table}: Sin problemas de locale`);
      }
    }
    
    // 3. Verificar el estado final
    console.log('\n📊 Verificación final...');
    
    const finalCheck = await client.query(`
      SELECT 
        'participants' as tabla,
        COUNT(*) as total,
        COUNT(CASE WHEN locale IS NULL THEN 1 END) as locale_null,
        COUNT(CASE WHEN locale = 'es' THEN 1 END) as locale_es
      FROM participants
      UNION ALL
      SELECT 
        'fields' as tabla,
        COUNT(*) as total,
        COUNT(CASE WHEN locale IS NULL THEN 1 END) as locale_null,
        COUNT(CASE WHEN locale = 'es' THEN 1 END) as locale_es
      FROM fields
      UNION ALL
      SELECT 
        'zones' as tabla,
        COUNT(*) as total,
        COUNT(CASE WHEN locale IS NULL THEN 1 END) as locale_null,
        COUNT(CASE WHEN locale = 'es' THEN 1 END) as locale_es
      FROM zones
      UNION ALL
      SELECT 
        'districts' as tabla,
        COUNT(*) as total,
        COUNT(CASE WHEN locale IS NULL THEN 1 END) as locale_null,
        COUNT(CASE WHEN locale = 'es' THEN 1 END) as locale_es
      FROM districts
      UNION ALL
      SELECT 
        'churches' as tabla,
        COUNT(*) as total,
        COUNT(CASE WHEN locale IS NULL THEN 1 END) as locale_null,
        COUNT(CASE WHEN locale = 'es' THEN 1 END) as locale_es
      FROM churches
    `);
    
    console.log('\n📋 Resumen final por tabla:');
    finalCheck.rows.forEach(row => {
      console.log(`   ${row.tabla.toUpperCase()}: ${row.total} total, ${row.locale_null} null, ${row.locale_es} con 'es'`);
    });
    
    // 4. Información sobre el ID específico del error
    console.log('\n🔍 Información sobre el ID del error original...');
    console.log(`   ID: bdfa292dae8445659186054b27ce20e8`);
    console.log(`   Este ID pertenece a: "Asociación Dominicana del Sur" (tabla: fields)`);
    console.log(`   ⚠️ El error sugiere que la aplicación está confundiendo IDs de fields con participants`);
    console.log(`   💡 Revisar el código frontend/API que maneja la creación de participants`);
    
    console.log('\n✅ Corrección de problemas de localidad completada!');
    
  } catch (error) {
    console.error('❌ Error durante la corrección:', error);
  } finally {
    await client.end();
    console.log('🔌 Conexión cerrada');
  }
}

fixLocaleIssues();
