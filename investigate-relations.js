const { Client } = require('pg');

async function investigateRelations() {
  const client = new Client({
    host: 'aws-0-us-east-2.pooler.supabase.com',
    port: 5432,
    database: 'postgres',
    user: 'postgres.ktykxlboodjvnlabbwoy',
    password: 'GranCH@1844',
    ssl: { rejectUnauthorized: false }
  });

  try {
    await client.connect();
    console.log('🔗 Conectado a la base de datos');

    // 1. Verificar todas las tablas relacionadas con Strapi
    console.log('\n📋 TODAS LAS TABLAS EN LA BASE DE DATOS:');
    const tables = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_type = 'BASE TABLE'
      ORDER BY table_name
    `);
    
    tables.rows.forEach(table => {
      console.log(`   - ${table.table_name}`);
    });

    // 2. Buscar tablas de relaciones
    console.log('\n🔗 TABLAS DE RELACIONES:');
    const relationTables = tables.rows.filter(table => 
      table.table_name.includes('_links') || 
      table.table_name.includes('_rel') ||
      table.table_name.includes('fields_') ||
      table.table_name.includes('zones_') ||
      table.table_name.includes('districts_')
    );
    
    relationTables.forEach(table => {
      console.log(`   - ${table.table_name}`);
    });

    // 3. Verificar estructura de cada tabla principal
    const mainTables = ['fields', 'zones', 'districts', 'churches'];
    
    for (const table of mainTables) {
      console.log(`\n🏗️ ESTRUCTURA COMPLETA DE ${table.toUpperCase()}:`);
      
      const columns = await client.query(`
        SELECT column_name, data_type, is_nullable, column_default
        FROM information_schema.columns 
        WHERE table_name = $1 
        ORDER BY ordinal_position
      `, [table]);
      
      columns.rows.forEach(col => {
        console.log(`   - ${col.column_name}: ${col.data_type} (${col.is_nullable === 'YES' ? 'nullable' : 'not null'}) ${col.column_default ? `default: ${col.column_default}` : ''}`);
      });

      // Buscar claves foráneas
      const foreignKeys = await client.query(`
        SELECT
          kcu.column_name,
          ccu.table_name AS foreign_table_name,
          ccu.column_name AS foreign_column_name
        FROM information_schema.table_constraints AS tc
        JOIN information_schema.key_column_usage AS kcu
          ON tc.constraint_name = kcu.constraint_name
          AND tc.table_schema = kcu.table_schema
        JOIN information_schema.constraint_column_usage AS ccu
          ON ccu.constraint_name = tc.constraint_name
          AND ccu.table_schema = tc.table_schema
        WHERE tc.constraint_type = 'FOREIGN KEY'
          AND tc.table_name = $1
      `, [table]);

      if (foreignKeys.rows.length > 0) {
        console.log(`   🔑 Claves foráneas:`);
        foreignKeys.rows.forEach(fk => {
          console.log(`     - ${fk.column_name} -> ${fk.foreign_table_name}.${fk.foreign_column_name}`);
        });
      }
    }

    // 4. Verificar si existe el campo específico con nombre
    console.log('\n🎯 BÚSQUEDA DEL CAMPO "Asociación Dominicana del Sur":');
    const southField = await client.query(`
      SELECT document_id, name, locale, created_at 
      FROM fields 
      WHERE name ILIKE '%sur%'
    `);

    if (southField.rows.length > 0) {
      console.log('✅ Campo encontrado:');
      southField.rows.forEach(field => {
        console.log(`   - Nombre: ${field.name}`);
        console.log(`   - Document ID: ${field.document_id}`);
        console.log(`   - Locale: ${field.locale}`);
        console.log(`   - Creado: ${field.created_at}`);
      });
    } else {
      console.log('❌ Campo "Asociación Dominicana del Sur" no encontrado');
    }

    // 5. Verificar tablas de relaciones específicas
    console.log('\n🔍 VERIFICACIÓN DE TABLAS DE RELACIONES ESPECÍFICAS:');
    
    const possibleRelationTables = [
      'fields_zones_links',
      'zones_districts_links', 
      'districts_churches_links',
      'zones_field_links',
      'districts_zone_links',
      'churches_district_links'
    ];

    for (const relTable of possibleRelationTables) {
      try {
        const exists = await client.query(`
          SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name = $1
          )
        `, [relTable]);

        if (exists.rows[0].exists) {
          const count = await client.query(`SELECT COUNT(*) FROM ${relTable}`);
          const sample = await client.query(`SELECT * FROM ${relTable} LIMIT 3`);
          
          console.log(`   ✅ ${relTable}: ${count.rows[0].count} registros`);
          if (sample.rows.length > 0) {
            console.log(`      Muestra:`, sample.rows[0]);
          }
        } else {
          console.log(`   ❌ ${relTable}: No existe`);
        }
      } catch (error) {
        console.log(`   ❌ ${relTable}: Error - ${error.message}`);
      }
    }

    // 6. Verificar content types en Strapi
    console.log('\n📝 CONTENT TYPES EN STRAPI:');
    const contentTypes = await client.query(`
      SELECT key, value 
      FROM strapi_core_store_settings 
      WHERE key = 'strapi_content_types_schema'
    `);

    if (contentTypes.rows.length > 0) {
      try {
        const schema = JSON.parse(contentTypes.rows[0].value);
        console.log('   Content Types encontrados:');
        Object.keys(schema).forEach(key => {
          if (key.includes('field') || key.includes('zone') || key.includes('district') || key.includes('church')) {
            console.log(`     - ${key}`);
          }
        });
      } catch (error) {
        console.log('   ❌ Error parseando schema de content types');
      }
    }

  } catch (error) {
    console.error('❌ Error general:', error.message);
  } finally {
    await client.end();
    console.log('\n🔌 Conexión cerrada');
  }
}

investigateRelations();
