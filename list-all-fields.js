const { Client } = require('pg');
require('dotenv').config();

// Configuración de la base de datos desde .env
const dbConfig = {
  host: process.env.DATABASE_HOST || 'aws-0-us-east-2.pooler.supabase.com',
  port: process.env.DATABASE_PORT || 5432,
  database: process.env.DATABASE_NAME || 'postgres',
  user: process.env.DATABASE_USERNAME || 'postgres.ktykxlboodjvnlabbwoy',
  password: process.env.DATABASE_PASSWORD || 'GranCH@1844',
  ssl: { rejectUnauthorized: false }
};

async function listAllFields() {
  const client = new Client(dbConfig);
  
  try {
    await client.connect();
    console.log('🔗 Conectado a la base de datos');
    
    console.log('\n📋 Listando todos los campos (fields) existentes:\n');
    
    // Listar todos los campos
    const allFields = await client.query(`
      SELECT document_id, name, acronym, type, locale, published_at, created_at 
      FROM fields 
      ORDER BY name
    `);
    
    console.log(`Total de campos encontrados: ${allFields.rows.length}\n`);
    
    allFields.rows.forEach((field, index) => {
      console.log(`${index + 1}. ${field.name}`);
      console.log(`   - Document ID: ${field.document_id}`);
      console.log(`   - Acrónimo: ${field.acronym}`);
      console.log(`   - Tipo: ${field.type}`);
      console.log(`   - Locale: ${field.locale}`);
      console.log(`   - Publicado: ${field.published_at}`);
      console.log(`   - Creado: ${field.created_at}`);
      console.log('');
    });
    
    // Verificar si el ID problemático existe en alguna otra tabla
    const problemId = 'bdfa292dae8445659186054b27ce20e8';
    console.log(`\n🔍 Buscando ID ${problemId} en otras tablas:\n`);
    
    const tables = ['zones', 'districts', 'churches', 'participants'];
    
    for (const table of tables) {
      try {
        const result = await client.query(`
          SELECT document_id, name, locale 
          FROM ${table} 
          WHERE document_id = $1
        `, [problemId]);
        
        if (result.rows.length > 0) {
          console.log(`   ✅ Encontrado en tabla ${table}:`);
          result.rows.forEach(row => {
            console.log(`      - Nombre: ${row.name}`);
            console.log(`      - Locale: ${row.locale}`);
          });
        } else {
          console.log(`   ❌ No encontrado en tabla ${table}`);
        }
      } catch (error) {
        if (table === 'participants') {
          // Para participants, intentar con first_name y last_name
          try {
            const result = await client.query(`
              SELECT document_id, first_name, last_name, email, locale 
              FROM ${table} 
              WHERE document_id = $1
            `, [problemId]);
            
            if (result.rows.length > 0) {
              console.log(`   ✅ Encontrado en tabla ${table}:`);
              result.rows.forEach(row => {
                console.log(`      - Nombre: ${row.first_name} ${row.last_name}`);
                console.log(`      - Email: ${row.email}`);
                console.log(`      - Locale: ${row.locale}`);
              });
            } else {
              console.log(`   ❌ No encontrado en tabla ${table}`);
            }
          } catch (err) {
            console.log(`   ❌ Error consultando tabla ${table}: ${err.message}`);
          }
        } else {
          console.log(`   ❌ Error consultando tabla ${table}: ${error.message}`);
        }
      }
    }
    
    // Corregir la configuración de locale por defecto
    console.log('\n🔧 Corrigiendo configuración de locale por defecto...');
    
    const updateResult = await client.query(`
      UPDATE strapi_core_store_settings 
      SET value = '"es"' 
      WHERE key = 'plugin_i18n_default_locale'
    `);
    
    if (updateResult.rowCount > 0) {
      console.log('   ✅ Configuración de locale por defecto actualizada de "en" a "es"');
    } else {
      console.log('   ⚠️ No se pudo actualizar la configuración de locale por defecto');
    }
    
    // Verificar la configuración actualizada
    const verifyConfig = await client.query(`
      SELECT key, value 
      FROM strapi_core_store_settings 
      WHERE key = 'plugin_i18n_default_locale'
    `);
    
    if (verifyConfig.rows.length > 0) {
      console.log(`   📋 Configuración actual: ${verifyConfig.rows[0].value}`);
    }
    
    console.log('\n📋 Análisis completado');
    
  } catch (error) {
    console.error('❌ Error durante el análisis:', error);
  } finally {
    await client.end();
    console.log('🔌 Conexión cerrada');
  }
}

listAllFields();
