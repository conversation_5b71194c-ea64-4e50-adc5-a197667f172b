{"kind": "collectionType", "collectionName": "churches", "info": {"singularName": "church", "pluralName": "churches", "displayName": "Church"}, "options": {"draftAndPublish": false}, "pluginOptions": {"content-manager": {"visible": true}, "content-type-builder": {"visible": true}, "i18n": {"localized": true}}, "attributes": {"name": {"type": "string", "required": true}, "district": {"type": "relation", "relation": "manyToOne", "target": "api::district.district"}}}