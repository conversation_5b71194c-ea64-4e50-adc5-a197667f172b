const { Client } = require('pg');

async function testStrapiAPI() {
  const client = new Client({
    host: 'aws-0-us-east-2.pooler.supabase.com',
    port: 5432,
    database: 'postgres',
    user: 'postgres.ktykxlboodjvnlabbwoy',
    password: 'GranCH@1844',
    ssl: { rejectUnauthorized: false }
  });

  try {
    await client.connect();
    console.log('🔗 Conectado a la base de datos');

    // 1. Simular consulta de Strapi para Fields
    console.log('\n📋 SIMULACIÓN DE CONSULTA STRAPI - FIELDS:');
    
    const fieldsQuery = await client.query(`
      SELECT 
        f.id,
        f.document_id,
        f.name,
        f.acronym,
        f.type,
        f.locale,
        f.published_at,
        f.created_at,
        f.updated_at
      FROM fields f
      WHERE f.published_at IS NOT NULL
      ORDER BY f.name
    `);

    console.log(`✅ Fields encontrados: ${fieldsQuery.rows.length}`);
    fieldsQuery.rows.forEach(field => {
      console.log(`   - ${field.name}`);
      console.log(`     ID: ${field.id}, Document ID: ${field.document_id}`);
      console.log(`     Locale: ${field.locale}, Publicado: ${field.published_at ? 'Sí' : 'No'}`);
      console.log('');
    });

    // 2. Verificar si hay problemas con los content types
    console.log('\n🔍 VERIFICACIÓN DE CONTENT TYPES:');
    
    const contentTypesSchema = await client.query(`
      SELECT value 
      FROM strapi_core_store_settings 
      WHERE key = 'strapi_content_types_schema'
    `);

    if (contentTypesSchema.rows.length > 0) {
      try {
        const schema = JSON.parse(contentTypesSchema.rows[0].value);
        
        // Verificar content types específicos
        const relevantTypes = ['api::field.field', 'api::zone.zone', 'api::district.district', 'api::church.church'];
        
        relevantTypes.forEach(type => {
          if (schema[type]) {
            console.log(`✅ ${type}: Configurado correctamente`);
            
            // Verificar si tiene i18n habilitado
            if (schema[type].pluginOptions && schema[type].pluginOptions.i18n) {
              console.log(`   - i18n: ${schema[type].pluginOptions.i18n.localized ? 'Habilitado' : 'Deshabilitado'}`);
            } else {
              console.log(`   - i18n: No configurado`);
            }
          } else {
            console.log(`❌ ${type}: No encontrado en schema`);
          }
        });
      } catch (error) {
        console.log('❌ Error parseando content types schema');
      }
    }

    // 3. Verificar migraciones de Strapi
    console.log('\n📝 VERIFICACIÓN DE MIGRACIONES:');
    
    const migrations = await client.query(`
      SELECT name, time 
      FROM strapi_migrations 
      ORDER BY time DESC 
      LIMIT 10
    `);

    console.log(`📊 Últimas migraciones ejecutadas (${migrations.rows.length}):`);
    migrations.rows.forEach(migration => {
      console.log(`   - ${migration.name} (${migration.time})`);
    });

    // 4. Verificar si hay datos en las tablas de componentes
    console.log('\n🧩 VERIFICACIÓN DE COMPONENTES:');
    
    const components = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name LIKE 'components_%'
    `);

    console.log(`📊 Tablas de componentes encontradas (${components.rows.length}):`);
    components.rows.forEach(comp => {
      console.log(`   - ${comp.table_name}`);
    });

    // 5. Verificar permisos y roles
    console.log('\n🔐 VERIFICACIÓN DE PERMISOS:');
    
    const adminUsers = await client.query(`SELECT COUNT(*) FROM admin_users`);
    const adminRoles = await client.query(`SELECT COUNT(*) FROM admin_roles`);
    const permissions = await client.query(`SELECT COUNT(*) FROM admin_permissions`);

    console.log(`   - Admin Users: ${adminUsers.rows[0].count}`);
    console.log(`   - Admin Roles: ${adminRoles.rows[0].count}`);
    console.log(`   - Admin Permissions: ${permissions.rows[0].count}`);

    // 6. Verificar configuración de plugins
    console.log('\n🔌 CONFIGURACIÓN DE PLUGINS:');
    
    const pluginConfigs = await client.query(`
      SELECT key, value 
      FROM strapi_core_store_settings 
      WHERE key LIKE 'plugin_%' 
      AND key NOT LIKE '%content_types%'
      ORDER BY key
    `);

    console.log(`📊 Configuraciones de plugins (${pluginConfigs.rows.length}):`);
    pluginConfigs.rows.forEach(config => {
      console.log(`   - ${config.key}`);
    });

    // 7. Verificar si Strapi está usando la configuración correcta
    console.log('\n⚙️ CONFIGURACIÓN GENERAL DE STRAPI:');
    
    const generalConfigs = await client.query(`
      SELECT key, value 
      FROM strapi_core_store_settings 
      WHERE key IN (
        'strapi_content_types_schema',
        'plugin_i18n_default_locale',
        'core_admin_auth'
      )
    `);

    generalConfigs.rows.forEach(config => {
      if (config.key === 'strapi_content_types_schema') {
        console.log(`   - ${config.key}: [Schema presente]`);
      } else {
        console.log(`   - ${config.key}: ${config.value}`);
      }
    });

    // 8. Proporcionar el ID correcto para la aplicación
    console.log('\n🎯 INFORMACIÓN PARA CORREGIR LA APLICACIÓN:');
    
    const correctField = await client.query(`
      SELECT document_id, name 
      FROM fields 
      WHERE name = 'Asociación Dominicana del Sur'
    `);

    if (correctField.rows.length > 0) {
      const field = correctField.rows[0];
      console.log(`✅ ID correcto para usar en la aplicación:`);
      console.log(`   - Campo: ${field.name}`);
      console.log(`   - ID Correcto: ${field.document_id}`);
      console.log(`   - ID Problemático: bdfa292dae8445659186054b27ce20e8`);
      console.log(`\n💡 ACCIÓN REQUERIDA:`);
      console.log(`   Actualizar la aplicación para usar el ID: ${field.document_id}`);
    }

  } catch (error) {
    console.error('❌ Error general:', error.message);
  } finally {
    await client.end();
    console.log('\n🔌 Conexión cerrada');
  }
}

testStrapiAPI();
