const { Client } = require('pg');

async function verifyRelationsAndFix() {
  const client = new Client({
    host: 'aws-0-us-east-2.pooler.supabase.com',
    port: 5432,
    database: 'postgres',
    user: 'postgres.ktykxlboodjvnlabbwoy',
    password: '<PERSON><PERSON>@1844',
    ssl: { rejectUnauthorized: false }
  });

  try {
    await client.connect();
    console.log('🔗 Conectado a la base de datos');

    // 1. Verificar las relaciones existentes
    console.log('\n🔗 VERIFICACIÓN DE RELACIONES EXISTENTES:');
    
    // Zones → Fields
    const zonesFieldLinks = await client.query(`
      SELECT 
        z.name as zone_name,
        f.name as field_name,
        zfl.zone_id,
        zfl.field_id
      FROM zones_field_lnk zfl
      JOIN zones z ON z.id = zfl.zone_id
      JOIN fields f ON f.id = zfl.field_id
      LIMIT 10
    `);
    
    console.log(`\n📊 Zones → Fields (${zonesFieldLinks.rows.length} relaciones encontradas):`);
    zonesFieldLinks.rows.forEach(rel => {
      console.log(`   - ${rel.zone_name} → ${rel.field_name}`);
    });

    // Districts → Zones
    const districtsZoneLinks = await client.query(`
      SELECT 
        d.name as district_name,
        z.name as zone_name,
        dzl.district_id,
        dzl.zone_id
      FROM districts_zone_lnk dzl
      JOIN districts d ON d.id = dzl.district_id
      JOIN zones z ON z.id = dzl.zone_id
      LIMIT 10
    `);
    
    console.log(`\n📊 Districts → Zones (${districtsZoneLinks.rows.length} relaciones encontradas):`);
    districtsZoneLinks.rows.forEach(rel => {
      console.log(`   - ${rel.district_name} → ${rel.zone_name}`);
    });

    // Churches → Districts
    const churchesDistrictLinks = await client.query(`
      SELECT 
        c.name as church_name,
        d.name as district_name,
        cdl.church_id,
        cdl.district_id
      FROM churches_district_lnk cdl
      JOIN churches c ON c.id = cdl.church_id
      JOIN districts d ON d.id = cdl.district_id
      LIMIT 10
    `);
    
    console.log(`\n📊 Churches → Districts (${churchesDistrictLinks.rows.length} relaciones encontradas):`);
    churchesDistrictLinks.rows.forEach(rel => {
      console.log(`   - ${rel.church_name} → ${rel.district_name}`);
    });

    // 2. Verificar jerarquía completa
    console.log('\n🏗️ VERIFICACIÓN DE JERARQUÍA COMPLETA:');
    
    const hierarchy = await client.query(`
      SELECT 
        f.name as field_name,
        f.document_id as field_document_id,
        z.name as zone_name,
        z.document_id as zone_document_id,
        d.name as district_name,
        d.document_id as district_document_id,
        c.name as church_name,
        c.document_id as church_document_id
      FROM fields f
      LEFT JOIN zones_field_lnk zfl ON f.id = zfl.field_id
      LEFT JOIN zones z ON z.id = zfl.zone_id
      LEFT JOIN districts_zone_lnk dzl ON z.id = dzl.zone_id
      LEFT JOIN districts d ON d.id = dzl.district_id
      LEFT JOIN churches_district_lnk cdl ON d.id = cdl.district_id
      LEFT JOIN churches c ON c.id = cdl.church_id
      WHERE f.name = 'Asociación Dominicana del Sur'
      LIMIT 5
    `);

    console.log('\n🎯 Jerarquía de "Asociación Dominicana del Sur":');
    if (hierarchy.rows.length > 0) {
      hierarchy.rows.forEach(row => {
        console.log(`   Field: ${row.field_name} (${row.field_document_id})`);
        console.log(`   └─ Zone: ${row.zone_name || 'N/A'} (${row.zone_document_id || 'N/A'})`);
        console.log(`      └─ District: ${row.district_name || 'N/A'} (${row.district_document_id || 'N/A'})`);
        console.log(`         └─ Church: ${row.church_name || 'N/A'} (${row.church_document_id || 'N/A'})`);
        console.log('');
      });
    } else {
      console.log('   ❌ No se encontró jerarquía completa');
    }

    // 3. Contar registros por nivel
    console.log('\n📊 RESUMEN DE REGISTROS POR NIVEL:');
    
    const fieldCount = await client.query('SELECT COUNT(*) FROM fields');
    const zoneCount = await client.query('SELECT COUNT(*) FROM zones');
    const districtCount = await client.query('SELECT COUNT(*) FROM districts');
    const churchCount = await client.query('SELECT COUNT(*) FROM churches');
    
    console.log(`   - Fields: ${fieldCount.rows[0].count}`);
    console.log(`   - Zones: ${zoneCount.rows[0].count}`);
    console.log(`   - Districts: ${districtCount.rows[0].count}`);
    console.log(`   - Churches: ${churchCount.rows[0].count}`);

    // 4. Verificar el ID correcto del campo problemático
    console.log('\n🎯 ID CORRECTO DEL CAMPO PROBLEMÁTICO:');
    const correctField = await client.query(`
      SELECT document_id, name, locale 
      FROM fields 
      WHERE name = 'Asociación Dominicana del Sur'
    `);

    if (correctField.rows.length > 0) {
      const field = correctField.rows[0];
      console.log(`✅ Campo encontrado:`);
      console.log(`   - Nombre: ${field.name}`);
      console.log(`   - ID Correcto: ${field.document_id}`);
      console.log(`   - ID Problemático Original: bdfa292dae8445659186054b27ce20e8`);
      console.log(`   - Locale: ${field.locale}`);
      
      if (field.document_id !== 'bdfa292dae8445659186054b27ce20e8') {
        console.log(`\n⚠️ PROBLEMA IDENTIFICADO:`);
        console.log(`   El ID en la base de datos (${field.document_id}) no coincide`);
        console.log(`   con el ID esperado por la aplicación (bdfa292dae8445659186054b27ce20e8)`);
        console.log(`\n💡 SOLUCIÓN:`);
        console.log(`   1. Actualizar el document_id en la base de datos, O`);
        console.log(`   2. Actualizar la aplicación para usar el ID correcto`);
      }
    }

    // 5. Verificar si Strapi puede leer los datos
    console.log('\n🔍 VERIFICACIÓN DE LECTURA DESDE STRAPI:');
    
    // Simular consulta como la haría Strapi
    const strapiQuery = await client.query(`
      SELECT 
        f.document_id,
        f.name,
        f.locale,
        f.published_at
      FROM fields f
      WHERE f.published_at IS NOT NULL
      AND f.locale = 'es'
      ORDER BY f.name
    `);

    console.log(`📋 Fields que Strapi debería poder leer (${strapiQuery.rows.length}):`);
    strapiQuery.rows.forEach(field => {
      console.log(`   - ${field.name} (${field.document_id})`);
    });

    // 6. Verificar configuración i18n
    console.log('\n🌐 CONFIGURACIÓN I18N:');
    const i18nConfig = await client.query(`
      SELECT key, value 
      FROM strapi_core_store_settings 
      WHERE key LIKE '%i18n%'
    `);

    i18nConfig.rows.forEach(config => {
      console.log(`   - ${config.key}: ${config.value}`);
    });

  } catch (error) {
    console.error('❌ Error general:', error.message);
  } finally {
    await client.end();
    console.log('\n🔌 Conexión cerrada');
  }
}

verifyRelationsAndFix();
