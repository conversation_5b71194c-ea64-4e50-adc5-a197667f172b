const { Client } = require('pg');

async function verifySpecificField() {
  const client = new Client({
    host: 'aws-0-us-west-1.pooler.supabase.com',
    port: 6543,
    database: 'postgres',
    user: 'postgres.hjoraiaoxecsyccwkskm',
    password: 'Iglesia2024*',
    ssl: { rejectUnauthorized: false }
  });

  try {
    await client.connect();
    console.log('🔗 Conectado a la base de datos');

    // Verificar el campo específico del error
    const specificFieldId = 'bdfa292dae8445659186054b27ce20e8';
    
    const result = await client.query(`
      SELECT document_id, name, locale, created_at 
      FROM fields 
      WHERE document_id = $1
    `, [specificFieldId]);

    console.log('\n🔍 Verificación del campo específico del error:');
    console.log(`ID buscado: ${specificFieldId}`);
    
    if (result.rows.length > 0) {
      console.log('✅ ¡CAMPO ENCONTRADO!');
      console.log('📋 Detalles:');
      result.rows.forEach(field => {
        console.log(`   - Nombre: ${field.name}`);
        console.log(`   - Document ID: ${field.document_id}`);
        console.log(`   - Locale: ${field.locale}`);
        console.log(`   - Creado: ${field.created_at}`);
      });
    } else {
      console.log('❌ Campo no encontrado');
    }

    // Verificar todos los fields para confirmar
    const allFields = await client.query(`
      SELECT document_id, name, locale 
      FROM fields 
      ORDER BY name
    `);

    console.log('\n📊 Todos los fields en la base de datos:');
    allFields.rows.forEach(field => {
      console.log(`   - ${field.name} (ID: ${field.document_id}, Locale: ${field.locale})`);
    });

    // Verificar configuración de locale
    const localeConfig = await client.query(`
      SELECT key, value 
      FROM strapi_core_store_settings 
      WHERE key LIKE '%i18n%'
    `);

    console.log('\n🌐 Configuración de i18n:');
    localeConfig.rows.forEach(config => {
      console.log(`   - ${config.key}: ${config.value}`);
    });

  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await client.end();
    console.log('🔌 Conexión cerrada');
  }
}

verifySpecificField();
