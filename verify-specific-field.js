const { Client } = require('pg');
require('dotenv').config();

// Configuración de la base de datos desde .env
const dbConfig = {
  host: process.env.DATABASE_HOST || 'aws-0-us-east-2.pooler.supabase.com',
  port: process.env.DATABASE_PORT || 5432,
  database: process.env.DATABASE_NAME || 'postgres',
  user: process.env.DATABASE_USERNAME || 'postgres.ktykxlboodjvnlabbwoy',
  password: process.env.DATABASE_PASSWORD || 'GranCH@1844',
  ssl: { rejectUnauthorized: false }
};

async function verifySpecificField() {
  const client = new Client(dbConfig);
  
  try {
    await client.connect();
    console.log('🔗 Conectado a la base de datos');
    
    const fieldId = 'bdfa292dae8445659186054b27ce20e8';
    console.log(`\n🔍 Verificando campo específico: ${fieldId}\n`);
    
    // 1. Buscar el campo específico en la tabla fields
    console.log('📋 Información del campo:');
    const fieldInfo = await client.query(`
      SELECT document_id, name, acronym, type, published_at, locale, created_at, updated_at 
      FROM fields 
      WHERE document_id = $1
    `, [fieldId]);
    
    if (fieldInfo.rows.length > 0) {
      const field = fieldInfo.rows[0];
      console.log(`   ✅ Campo encontrado:`);
      console.log(`      - Document ID: ${field.document_id}`);
      console.log(`      - Nombre: ${field.name}`);
      console.log(`      - Acrónimo: ${field.acronym}`);
      console.log(`      - Tipo: ${field.type}`);
      console.log(`      - Locale: ${field.locale}`);
      console.log(`      - Publicado: ${field.published_at}`);
      console.log(`      - Creado: ${field.created_at}`);
      console.log(`      - Actualizado: ${field.updated_at}`);
    } else {
      console.log(`   ❌ Campo con ID ${fieldId} NO encontrado en la tabla fields`);
    }
    
    // 2. Verificar si hay duplicados del mismo document_id
    console.log('\n🔍 Verificando duplicados del mismo document_id:');
    const duplicates = await client.query(`
      SELECT document_id, name, locale, created_at 
      FROM fields 
      WHERE document_id = $1
      ORDER BY created_at
    `, [fieldId]);
    
    console.log(`   Total registros con este document_id: ${duplicates.rows.length}`);
    duplicates.rows.forEach((row, index) => {
      console.log(`   ${index + 1}. Nombre: ${row.name}, Locale: ${row.locale}, Creado: ${row.created_at}`);
    });
    
    // 3. Verificar todas las variaciones de locale para este campo
    console.log('\n🔍 Verificando todas las variaciones de locale:');
    const allVariations = await client.query(`
      SELECT document_id, name, locale, published_at 
      FROM fields 
      WHERE name = 'Asociación Dominicana del Sur'
      ORDER BY locale, created_at
    `);
    
    console.log(`   Total variaciones del campo "Asociación Dominicana del Sur": ${allVariations.rows.length}`);
    allVariations.rows.forEach((row, index) => {
      console.log(`   ${index + 1}. Document ID: ${row.document_id}, Locale: ${row.locale}, Publicado: ${row.published_at}`);
    });
    
    // 4. Verificar el estado de i18n en Strapi
    console.log('\n🔍 Verificando configuración de localización en la base de datos:');
    
    // Verificar si existe la tabla strapi_core_store_settings para configuración i18n
    const i18nConfig = await client.query(`
      SELECT key, value 
      FROM strapi_core_store_settings 
      WHERE key LIKE '%i18n%' OR key LIKE '%locale%'
    `);
    
    if (i18nConfig.rows.length > 0) {
      console.log(`   ✅ Configuración i18n encontrada:`);
      i18nConfig.rows.forEach(row => {
        console.log(`      - ${row.key}: ${row.value}`);
      });
    } else {
      console.log(`   ⚠️ No se encontró configuración i18n en strapi_core_store_settings`);
    }
    
    // 5. Verificar si hay registros con locale null en fields
    console.log('\n🔍 Verificando registros con locale null en fields:');
    const nullLocales = await client.query(`
      SELECT document_id, name, locale 
      FROM fields 
      WHERE locale IS NULL
    `);
    
    if (nullLocales.rows.length > 0) {
      console.log(`   ❌ Encontrados ${nullLocales.rows.length} campos con locale null:`);
      nullLocales.rows.forEach(row => {
        console.log(`      - ${row.name} (ID: ${row.document_id})`);
      });
    } else {
      console.log(`   ✅ No hay campos con locale null`);
    }
    
    console.log('\n📋 Resumen del análisis completado');
    
  } catch (error) {
    console.error('❌ Error durante la verificación:', error);
  } finally {
    await client.end();
    console.log('🔌 Conexión cerrada');
  }
}

verifySpecificField();
